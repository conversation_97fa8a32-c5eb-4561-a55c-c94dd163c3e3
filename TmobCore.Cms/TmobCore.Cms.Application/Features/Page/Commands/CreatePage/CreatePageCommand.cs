using MediatR;
using TmobCore.Cms.Application.Models.Page;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.Page.Commands.CreatePage
{
    public class CreatePageCommand : IRequest<ActionResponse<bool>>
    {
        public List<CreatePageRequest> Pages { get; set; } = new();
    }

    public class CreatePageRequest : BaseRequest
    {
        public string Title { get; set; }
        public Guid? ParentPageId { get; set; }
        public string Slug { get; set; }
        public int Order { get; set; }
        public Guid? ImageId { get; set; }
        public BaseStatus Status { get; set; }
        public DateTime PublishDate { get; set; }
        public List<PageContentRequest>? Contents { get; set; } = new List<PageContentRequest>();
    }
}